#!/usr/bin/env node

/**
 * Script để sửa lỗi syntax cụ thể
 */

const fs = require('fs');

const filePath = 'src/views/model-ai/provider/utils/auth-api.ts';

function fixSpecificFile() {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    
    // Sửa lỗi cụ thể: (`delete", "path") thành ("delete", `path`)
    content = content.replace(/\(`delete",\s*"([^"]*\$\{[^}]+\}[^"]*)"\)/g, '("delete", `$1`)');
    
    // Sửa các lỗi tương tự khác
    content = content.replace(/\(`([^"]+)",\s*"([^"]*\$\{[^}]+\}[^"]*)"\)/g, '("$1", `$2`)');
    
    fs.writeFileSync(filePath, content, 'utf8');
    console.log(`✅ Fixed syntax errors in: ${filePath}`);
    
    // <PERSON><PERSON><PERSON> tra kết quả
    const newContent = fs.readFileSync(filePath, 'utf8');
    const lines = newContent.split('\n');
    const line45 = lines[44]; // Line 45 (0-indexed)
    console.log(`Line 45 after fix: ${line45}`);
    
  } catch (error) {
    console.error(`❌ Error:`, error.message);
  }
}

fixSpecificFile();
