#!/usr/bin/env node

/**
 * Script để sửa các lỗi syntax còn lại trong template literals
 */

const fs = require('fs');
const path = require('path');

// <PERSON>h sách các thư mục cần kiểm tra
const checkDirectories = [
  'src/views'
];

// Patterns để sửa các lỗi syntax
const syntaxFixes = [
  {
    // Sửa: (`method", "path") thành ("method", `path`)
    search: /\(`([^"]+)",\s*"([^"]*\$\{[^}]+\}[^"]*)"\)/g,
    replace: '("$1", `$2`)',
    description: 'Fix mixed quote syntax in template literals'
  },
  {
    // Sửa: ("method", "path${var}") thành ("method", `path${var}`)
    search: /\("([^"]+)",\s*"([^"]*\$\{[^}]+\}[^"]*)"\)/g,
    replace: '("$1", `$2`)',
    description: 'Fix double quotes around template literals'
  }
];

function fixFile(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let hasChanges = false;
    let changeCount = 0;
    
    syntaxFixes.forEach(fix => {
      const matches = content.match(fix.search);
      if (matches) {
        const originalContent = content;
        content = content.replace(fix.search, fix.replace);
        if (content !== originalContent) {
          hasChanges = true;
          changeCount += matches.length;
          console.log(`   ✅ ${fix.description}: ${matches.length} fixes`);
        }
      }
    });
    
    if (hasChanges) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`✅ Fixed ${changeCount} syntax errors in: ${filePath}`);
      return changeCount;
    }
    
    return 0;
  } catch (error) {
    console.error(`❌ Error fixing file ${filePath}:`, error.message);
    return 0;
  }
}

function checkFile(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    
    // Tìm các pattern có vấn đề
    const problemPatterns = [
      /\(`[^"]+",\s*"[^"]*\$\{[^}]+\}[^"]*"\)/g,  // (`method", "path${var}")
      /\("[^"]+",\s*"[^"]*\$\{[^}]+\}[^"]*"\)/g   // ("method", "path${var}")
    ];
    
    let hasProblems = false;
    
    problemPatterns.forEach(pattern => {
      const matches = content.match(pattern);
      if (matches && matches.length > 0) {
        if (!hasProblems) {
          console.log(`\n❌ File: ${filePath}`);
          hasProblems = true;
        }
        matches.forEach(match => {
          const lines = content.split('\n');
          const lineNumber = lines.findIndex(line => line.includes(match)) + 1;
          console.log(`   Line ${lineNumber}: ${match}`);
        });
      }
    });
    
    return !hasProblems;
  } catch (error) {
    console.error(`Error checking file ${filePath}:`, error.message);
    return false;
  }
}

function processDirectory(dirPath, mode = 'check') {
  let results = { total: 0, fixed: 0, clean: 0 };
  
  function walkDir(currentPath) {
    const items = fs.readdirSync(currentPath);
    
    items.forEach(item => {
      const fullPath = path.join(currentPath, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory()) {
        walkDir(fullPath);
      } else if (item.endsWith('.ts') || item.endsWith('.js') || item.endsWith('.vue')) {
        results.total++;
        
        if (mode === 'fix') {
          const fixCount = fixFile(fullPath);
          if (fixCount > 0) {
            results.fixed++;
          } else {
            results.clean++;
          }
        } else {
          const isClean = checkFile(fullPath);
          if (isClean) {
            results.clean++;
          }
        }
      }
    });
  }
  
  if (fs.existsSync(dirPath)) {
    walkDir(dirPath);
  } else {
    console.log(`⚠️  Directory not found: ${dirPath}`);
  }
  
  return results;
}

function main() {
  console.log('🔍 Checking for syntax errors in template literals...\n');
  
  // First, check for issues
  let totalResults = { total: 0, fixed: 0, clean: 0 };
  
  checkDirectories.forEach(dir => {
    console.log(`📁 Checking directory: ${dir}`);
    const results = processDirectory(dir, 'check');
    totalResults.total += results.total;
    totalResults.clean += results.clean;
    
    if (results.clean === results.total) {
      console.log(`   ✅ All ${results.total} files are clean`);
    } else {
      console.log(`   ⚠️  ${results.total - results.clean} files have syntax errors`);
    }
  });
  
  const hasIssues = totalResults.clean < totalResults.total;
  
  if (hasIssues) {
    console.log('\n🔧 Fixing syntax errors...\n');
    
    // Fix the issues
    let fixResults = { total: 0, fixed: 0, clean: 0 };
    
    checkDirectories.forEach(dir => {
      console.log(`📁 Fixing directory: ${dir}`);
      const results = processDirectory(dir, 'fix');
      fixResults.total += results.total;
      fixResults.fixed += results.fixed;
      fixResults.clean += results.clean;
    });
    
    console.log('\n' + '='.repeat(50));
    console.log(`📊 FIX SUMMARY:`);
    console.log(`   Total files processed: ${fixResults.total}`);
    console.log(`   Files fixed: ${fixResults.fixed}`);
    console.log(`   Files already clean: ${fixResults.clean}`);
    
    if (fixResults.fixed > 0) {
      console.log('\n🎉 Syntax errors have been fixed!');
    }
  } else {
    console.log('\n' + '='.repeat(50));
    console.log(`📊 CHECK SUMMARY:`);
    console.log(`   Total files checked: ${totalResults.total}`);
    console.log(`   Clean files: ${totalResults.clean}`);
    console.log(`   Files with syntax errors: 0`);
    console.log('\n✅ No syntax errors found!');
  }
}

// Run the script
main();
