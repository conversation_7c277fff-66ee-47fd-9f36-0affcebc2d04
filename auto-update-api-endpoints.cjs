#!/usr/bin/env node

/**
 * <PERSON>ript tự động cập nhật tất cả các API endpoints từ /api thành /api/v1
 */

const fs = require('fs');
const path = require('path');

// <PERSON>h sách các thư mục cần cập nhật
const updateDirectories = [
  'src/views'
];

// Pattern để tìm và thay thế
const patterns = [
  {
    search: /["'`]\/api\/auth\//g,
    replace: '"/api/v1/auth/'
  },
  {
    search: /["'`]\/api\/(?!v1)/g,
    replace: '"/api/v1/'
  },
  {
    search: /`\/api\/auth\//g,
    replace: '`/api/v1/auth/'
  },
  {
    search: /`\/api\/(?!v1)/g,
    replace: '`/api/v1/'
  }
];

function updateFile(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let hasChanges = false;
    
    patterns.forEach(pattern => {
      const originalContent = content;
      content = content.replace(pattern.search, pattern.replace);
      if (content !== originalContent) {
        hasChanges = true;
      }
    });
    
    if (hasChanges) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`✅ Updated: ${filePath}`);
      return true;
    }
    
    return false;
  } catch (error) {
    console.error(`❌ Error updating file ${filePath}:`, error.message);
    return false;
  }
}

function updateDirectory(dirPath) {
  let updatedFiles = 0;
  let totalFiles = 0;
  
  function walkDir(currentPath) {
    const items = fs.readdirSync(currentPath);
    
    items.forEach(item => {
      const fullPath = path.join(currentPath, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory()) {
        walkDir(fullPath);
      } else if (item.endsWith('.ts') || item.endsWith('.js') || item.endsWith('.vue')) {
        totalFiles++;
        if (updateFile(fullPath)) {
          updatedFiles++;
        }
      }
    });
  }
  
  if (fs.existsSync(dirPath)) {
    walkDir(dirPath);
  } else {
    console.log(`⚠️  Directory not found: ${dirPath}`);
  }
  
  return { updated: updatedFiles, total: totalFiles };
}

function main() {
  console.log('🔄 Auto-updating API endpoints...\n');
  
  let totalUpdated = 0;
  let totalFiles = 0;
  
  updateDirectories.forEach(dir => {
    console.log(`📁 Processing directory: ${dir}`);
    const result = updateDirectory(dir);
    totalUpdated += result.updated;
    totalFiles += result.total;
    console.log(`   Updated: ${result.updated}/${result.total} files\n`);
  });
  
  console.log('='.repeat(50));
  console.log(`📊 SUMMARY:`);
  console.log(`   Total files processed: ${totalFiles}`);
  console.log(`   Files updated: ${totalUpdated}`);
  console.log(`   Files unchanged: ${totalFiles - totalUpdated}`);
  
  if (totalUpdated > 0) {
    console.log('\n🎉 API endpoints have been updated successfully!');
    console.log('Please run the check script to verify all changes.');
  } else {
    console.log('\n✅ No files needed updating.');
  }
}

// Chạy script
main();
