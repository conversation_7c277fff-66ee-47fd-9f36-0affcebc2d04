import { http } from "@/utils/http";
import { useConvertKeyToSnake } from "@/utils/helpers";
import type { Result } from "@/utils/response";

/*
 ***************************
 *   Read Operations (GET)
 ***************************
 */
export const getUsers = (params?: any) => {
  return http.request<Result>("get", "/api/v1/auth/users", { params });
};

export const getUserById = (id: number) => {
  return http.request<Result>("get", `/api/v1/auth/users/${id}`);
};

export const getUsersDropdown = () => {
  return http.request<Result>(`get", "/api/v1/auth/users/dropdown");
};

/*
 ***************************
 *   Create & Update Operations
 ***************************
 */
export const createUser = (data: any) => {
  return http.request<Result>("post", "/api/v1/auth/users", {
    data: useConvertKeyToSnake(data)
  });
};

export const updateUserById = (id: number, data: any) => {
  return http.request<Result>("put", `/api/v1/auth/users/${id}`, {
    data: useConvertKeyToSnake(data)
  });
};

/*
 ***************************
 *   Soft Delete Operations
 ***************************
 */
export const deleteUser = (id: number) => {
  return http.request<Result>(`delete", "/api/v1/auth/users/${id}/delete`);
};

export const bulkDeleteUsers = (data: { ids: number[] }) => {
  return http.request<Result>("delete", "/api/v1/auth/users/bulk/delete", {
    data: useConvertKeyToSnake(data)
  });
};

/*
 ***************************
 *   Permanent Delete Operations
 ***************************
 */
export const deleteUserPermanent = (id: number) => {
  return http.request<Result>("delete", `/api/v1/auth/users/${id}/force`);
};

export const bulkDeleteUsersPermanent = (data: { ids: number[] }) => {
  return http.request<Result>(`delete", "/api/v1/auth/users/bulk/force", {
    data: useConvertKeyToSnake(data)
  });
};

/*
 ***************************
 *   Restore Operations
 ***************************
 */
export const restoreUser = (id: number) => {
  return http.request<Result>("put", `/api/v1/auth/users/${id}/restore`);
};

export const bulkRestoreUsers = (data: { ids: number[] }) => {
  return http.request<Result>(`put", "/api/v1/auth/users/bulk/restore", {
    data: useConvertKeyToSnake(data)
  });
};
