#!/usr/bin/env node

/**
 * Script để sửa lỗi template literals trong API endpoints
 * Sửa từ: "get", "/api/v1/auth/providers/${id}"
 * Thành: "get", `/api/v1/auth/providers/${id}`
 */

const fs = require('fs');
const path = require('path');

// <PERSON>h sách các thư mục cần kiểm tra
const checkDirectories = [
  'src/api',
  'src/views',
  'src/utils/http',
  'src/widget'
];

// Pattern để tìm các template literals sai
const patterns = [
  {
    // Tìm: "method", "/api/...${variable}..."
    search: /("(?:get|post|put|delete|patch)",\s*)"(\/api\/[^"]*\$\{[^}]+\}[^"]*)"/g,
    replace: '$1`$2`',
    description: 'Fix template literals with variables in API paths'
  },
  {
    // Tìm: 'method', '/api/...${variable}...'
    search: /('(?:get|post|put|delete|patch)',\s*)'(\/api\/[^']*\$\{[^}]+\}[^']*)'/g,
    replace: '$1`$2`',
    description: 'Fix template literals with variables in API paths (single quotes)'
  }
];

function fixFile(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let hasChanges = false;
    let changeCount = 0;
    
    patterns.forEach(pattern => {
      const matches = content.match(pattern.search);
      if (matches) {
        const originalContent = content;
        content = content.replace(pattern.search, pattern.replace);
        if (content !== originalContent) {
          hasChanges = true;
          changeCount += matches.length;
          console.log(`   ✅ ${pattern.description}: ${matches.length} fixes`);
        }
      }
    });
    
    if (hasChanges) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`✅ Fixed ${changeCount} issues in: ${filePath}`);
      return changeCount;
    }
    
    return 0;
  } catch (error) {
    console.error(`❌ Error fixing file ${filePath}:`, error.message);
    return 0;
  }
}

function checkFile(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    
    // Tìm các pattern có vấn đề
    const problemPatterns = [
      /"(?:get|post|put|delete|patch)",\s*"\/api\/[^"]*\$\{[^}]+\}[^"]*"/g,
      /'(?:get|post|put|delete|patch)',\s*'\/api\/[^']*\$\{[^}]+\}[^']*'/g
    ];
    
    let hasProblems = false;
    
    problemPatterns.forEach(pattern => {
      const matches = content.match(pattern);
      if (matches && matches.length > 0) {
        if (!hasProblems) {
          console.log(`\n❌ File: ${filePath}`);
          hasProblems = true;
        }
        matches.forEach(match => {
          const lines = content.split('\n');
          const lineNumber = lines.findIndex(line => line.includes(match)) + 1;
          console.log(`   Line ${lineNumber}: ${match}`);
        });
      }
    });
    
    return !hasProblems;
  } catch (error) {
    console.error(`Error checking file ${filePath}:`, error.message);
    return false;
  }
}

function processDirectory(dirPath, mode = 'check') {
  let results = { total: 0, fixed: 0, clean: 0 };
  
  function walkDir(currentPath) {
    const items = fs.readdirSync(currentPath);
    
    items.forEach(item => {
      const fullPath = path.join(currentPath, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory()) {
        walkDir(fullPath);
      } else if (item.endsWith('.ts') || item.endsWith('.js') || item.endsWith('.vue')) {
        results.total++;
        
        if (mode === 'fix') {
          const fixCount = fixFile(fullPath);
          if (fixCount > 0) {
            results.fixed++;
          } else {
            results.clean++;
          }
        } else {
          const isClean = checkFile(fullPath);
          if (isClean) {
            results.clean++;
          }
        }
      }
    });
  }
  
  if (fs.existsSync(dirPath)) {
    walkDir(dirPath);
  } else {
    console.log(`⚠️  Directory not found: ${dirPath}`);
  }
  
  return results;
}

function main() {
  console.log('🔍 Checking for template literal issues...\n');
  
  // First, check for issues
  let totalResults = { total: 0, fixed: 0, clean: 0 };
  
  checkDirectories.forEach(dir => {
    console.log(`📁 Checking directory: ${dir}`);
    const results = processDirectory(dir, 'check');
    totalResults.total += results.total;
    totalResults.clean += results.clean;
    
    if (results.clean === results.total) {
      console.log(`   ✅ All ${results.total} files are clean`);
    } else {
      console.log(`   ⚠️  ${results.total - results.clean} files have issues`);
    }
  });
  
  const hasIssues = totalResults.clean < totalResults.total;
  
  if (hasIssues) {
    console.log('\n🔧 Fixing template literal issues...\n');
    
    // Fix the issues
    let fixResults = { total: 0, fixed: 0, clean: 0 };
    
    checkDirectories.forEach(dir => {
      console.log(`📁 Fixing directory: ${dir}`);
      const results = processDirectory(dir, 'fix');
      fixResults.total += results.total;
      fixResults.fixed += results.fixed;
      fixResults.clean += results.clean;
    });
    
    console.log('\n' + '='.repeat(50));
    console.log(`📊 FIX SUMMARY:`);
    console.log(`   Total files processed: ${fixResults.total}`);
    console.log(`   Files fixed: ${fixResults.fixed}`);
    console.log(`   Files already clean: ${fixResults.clean}`);
    
    if (fixResults.fixed > 0) {
      console.log('\n🎉 Template literal issues have been fixed!');
      console.log('Please review the changes and test your application.');
    }
  } else {
    console.log('\n' + '='.repeat(50));
    console.log(`📊 CHECK SUMMARY:`);
    console.log(`   Total files checked: ${totalResults.total}`);
    console.log(`   Clean files: ${totalResults.clean}`);
    console.log(`   Files with issues: 0`);
    console.log('\n✅ No template literal issues found!');
  }
}

// Run the script
main();
