import { http } from "@/utils/http";
import { useConvertKeyToSnake } from "@/utils/helpers";
import type { Result } from "@/utils/response";
import type { FormItemProps } from "@/views/system/sidebars/utils/type";

/*
 ***************************
 *   Read Operations (GET)
 ***************************
 */
export const getSidebars = (params?: any) => {
  return http.request<Result>("get", "/api/v1/auth/sidebars", { params });
};

export const getSidebarById = (id: number) => {
  return http.request<Result>("get", `/api/v1/auth/sidebars/${id}`);
};

export const getSidebarsDropdown = () => {
  return http.request<r>(`get", "/api/v1/auth/sidebars/dropdown");
};

/*
 ***************************
 *   Create & Update Operations
 ***************************
 */
export const createSidebar = (data: FormItemProps) => {
  return http.request<Result>("post", "/api/v1/auth/sidebars", {
    data: useConvertKeyToSnake(data)
  });
};

export const updateSidebarById = (id: number, data: FormItemProps) => {
  return http.request<Result>("put", `/api/v1/auth/sidebars/${id}`, {
    data: useConvertKeyToSnake(data)
  });
};

/*
 ***************************
 *   Soft Delete Operations
 ***************************
 */
export const deleteSidebar = (id: number) => {
  return http.request<Result>(`delete", "/api/v1/auth/sidebars/${id}/delete`);
};

export const bulkDeleteSidebars = (data: { ids: number[] }) => {
  return http.request<Result>("delete", "/api/v1/auth/sidebars/bulk/delete", {
    data: useConvertKeyToSnake(data)
  });
};

/*
 ***************************
 *   Permanent Delete Operations
 ***************************
 */
export const deleteSidebarPermanent = (id: number) => {
  return http.request<Result>("delete", `/api/v1/auth/sidebars/${id}/force`);
};

export const bulkDeleteSidebarsPermanent = (data: { ids: number[] }) => {
  return http.request<Result>(`delete", "/api/v1/auth/sidebars/bulk/force", {
    data: useConvertKeyToSnake(data)
  });
};

/*
 ***************************
 *   Restore Operations
 ***************************
 */
export const restoreSidebar = (id: number) => {
  return http.request<Result>("put", `/api/v1/auth/sidebars/${id}/restore`);
};

export const bulkRestoreSidebars = (data: { ids: number[] }) => {
  return http.request<Result>(`put", "/api/v1/auth/sidebars/bulk/restore", {
    data: useConvertKeyToSnake(data)
  });
};

/* ***************************
 * Dropdown/Select Options
 *************************** */

export const dropdownSidebars = () => {
  return http.request<Result>("get", "/api/v1/auth/sidebars/dropdown");
};
