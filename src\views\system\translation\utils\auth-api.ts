import { http } from "@/utils/http";
import type { Result } from "@/utils/response";

type ResultTable = {
  success: boolean;
  data?: Array<any>;
  message?: string;
  total?: number;
  current_page?: number;
  per_page?: number;
  last_page?: number;
};

/** Get translations list (Spatie\Translatable format) */
export const getTranslations = (data?: object): Promise<ResultTable> => {
  return http.request<ResultTable>("get", "/api/v1/translations", {
    params: data
  });
};

/** Get available locales */
export const getAvailableLocales = (): Promise<Result> => {
  return http.request<Result>("get", "/api/v1/translations/locales");
};

/** Get translation groups */
export const getTranslationGroups = (): Promise<Result> => {
  return http.request<Result>("get", "/api/v1/translations/groups");
};

/** Create or update translation key with multiple locales */
export const saveTranslation = (data?: object): Promise<Result> => {
  return http.request<Result>("post", "/api/v1/translations", { data });
};

/** Update specific translation for a key and locale */
export const updateTranslation = (
  key: string,
  locale: string,
  data?: object
): Promise<Result> => {
  return http.request<Result>("put", `/api/v1/translations/${key}/${locale}`, {
    data
  });
};

/** Delete translation key (all locales) */
export const deleteTranslation = (id: number): Promise<Result> => {
  return http.request<Result>(`delete", "/api/v1/translations/${id}`);
};

/** Delete specific translation for a locale */
export const deleteTranslationLocale = (
  key: string,
  locale: string
): Promise<Result> => {
  return http.request<Result>("delete", `/api/v1/translations/${key}/${locale}`);
};

/** Bulk delete translation keys */
export const bulkDeleteTranslations = (data: {
  ids: number[];
}): Promise<Result> => {
  return http.request<Result>(`delete", "/api/v1/translations/bulk", { data });
};

/** Import translations from file */
export const importTranslations = (data: FormData): Promise<Result> => {
  return http.request<Result>("post", "/api/v1/translations/import", {
    data,
    headers: { "Content-Type": "multipart/form-data" }
  });
};

/** Export translations to file */
export const exportTranslations = (params?: object): Promise<Blob> => {
  return http.request<Blob>("get", "/api/v1/translations/export", {
    params,
    responseType: "blob"
  });
};

/** Scan for missing translations */
export const scanMissingTranslations = (): Promise<Result> => {
  return http.request<Result>("post", "/api/v1/translations/scan");
};
