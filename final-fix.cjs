#!/usr/bin/env node

/**
 * Script cuối cùng để sửa tất cả lỗi template literal
 */

const fs = require('fs');
const path = require('path');

// <PERSON>h sách các thư mục cần kiểm tra
const checkDirectories = [
  'src/views'
];

function fixAllTemplateIssues(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let originalContent = content;
    
    // Pattern 1: Sửa (`method", "path${var}") thành ("method", `path${var}`)
    content = content.replace(/\(`([^"]+)",\s*"([^"]*\$\{[^}]+\}[^"]*)"\)/g, '("$1", `$2`)');
    
    // Pattern 2: Sửa ("method", "path${var}") thành ("method", `path${var}`)
    content = content.replace(/\("([^"]+)",\s*"([^"]*\$\{[^}]+\}[^"]*)"\)/g, '("$1", `$2`)');
    
    // Pattern 3: Sửa ('method', 'path${var}') thành ('method', `path${var}`)
    content = content.replace(/\('([^']+)',\s*'([^']*\$\{[^}]+\}[^']*)'\)/g, "('$1', `$2`)");
    
    // Pattern 4: Sửa (`method', 'path${var}') thành ('method', `path${var}`)
    content = content.replace(/\(`([^']+)',\s*'([^']*\$\{[^}]+\}[^']*)'\)/g, "('$1', `$2`)");
    
    if (content !== originalContent) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`✅ Fixed: ${filePath}`);
      return true;
    }
    
    return false;
  } catch (error) {
    console.error(`❌ Error fixing ${filePath}:`, error.message);
    return false;
  }
}

function checkForIssues(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    
    // Tìm tất cả các pattern có vấn đề
    const issues = [];
    
    // Pattern 1: (`method", "path${var}")
    const pattern1 = /\(`[^"]+",\s*"[^"]*\$\{[^}]+\}[^"]*"\)/g;
    let matches = content.match(pattern1);
    if (matches) issues.push(...matches);
    
    // Pattern 2: ("method", "path${var}")
    const pattern2 = /\("[^"]+",\s*"[^"]*\$\{[^}]+\}[^"]*"\)/g;
    matches = content.match(pattern2);
    if (matches) issues.push(...matches);
    
    // Pattern 3: ('method', 'path${var}')
    const pattern3 = /\('[^']+',\s*'[^']*\$\{[^}]+\}[^']*'\)/g;
    matches = content.match(pattern3);
    if (matches) issues.push(...matches);
    
    // Pattern 4: (`method', 'path${var}')
    const pattern4 = /\(`[^']+',\s*'[^']*\$\{[^}]+\}[^']*'\)/g;
    matches = content.match(pattern4);
    if (matches) issues.push(...matches);
    
    if (issues.length > 0) {
      console.log(`\n❌ Issues in ${filePath}:`);
      issues.forEach(issue => {
        const lines = content.split('\n');
        const lineNumber = lines.findIndex(line => line.includes(issue)) + 1;
        console.log(`   Line ${lineNumber}: ${issue}`);
      });
      return false;
    }
    
    return true;
  } catch (error) {
    console.error(`Error checking ${filePath}:`, error.message);
    return false;
  }
}

function processDirectory(dirPath, mode = 'check') {
  let results = { total: 0, fixed: 0, clean: 0 };
  
  function walkDir(currentPath) {
    const items = fs.readdirSync(currentPath);
    
    items.forEach(item => {
      const fullPath = path.join(currentPath, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory()) {
        walkDir(fullPath);
      } else if (item.endsWith('.ts') || item.endsWith('.js') || item.endsWith('.vue')) {
        results.total++;
        
        if (mode === 'fix') {
          if (fixAllTemplateIssues(fullPath)) {
            results.fixed++;
          } else {
            results.clean++;
          }
        } else {
          if (checkForIssues(fullPath)) {
            results.clean++;
          }
        }
      }
    });
  }
  
  if (fs.existsSync(dirPath)) {
    walkDir(dirPath);
  }
  
  return results;
}

function main() {
  console.log('🔍 Final check and fix for template literal issues...\n');
  
  // Check for issues first
  let hasIssues = false;
  checkDirectories.forEach(dir => {
    console.log(`📁 Checking ${dir}:`);
    const results = processDirectory(dir, 'check');
    if (results.clean < results.total) {
      hasIssues = true;
    }
  });
  
  if (hasIssues) {
    console.log('\n🔧 Fixing all issues...\n');
    
    // Fix all issues
    checkDirectories.forEach(dir => {
      console.log(`📁 Fixing ${dir}:`);
      processDirectory(dir, 'fix');
    });
    
    console.log('\n🔍 Final verification...\n');
    
    // Final check
    let allClean = true;
    checkDirectories.forEach(dir => {
      console.log(`📁 Final check ${dir}:`);
      const results = processDirectory(dir, 'check');
      if (results.clean < results.total) {
        allClean = false;
      } else {
        console.log(`   ✅ All ${results.total} files are clean`);
      }
    });
    
    if (allClean) {
      console.log('\n🎉 All template literal issues have been fixed!');
    } else {
      console.log('\n⚠️  Some issues still remain.');
    }
  } else {
    console.log('\n✅ No template literal issues found!');
  }
}

main();
