#!/usr/bin/env node

/**
 * Script để sửa thủ công file có vấn đề
 */

const fs = require('fs');

const filePath = 'src/views/model-ai/provider/utils/auth-api.ts';

function manualFix() {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    
    // Thay thế dòng 45 cụ thể
    const lines = content.split('\n');
    
    // Tìm và sửa dòng có vấn đề
    for (let i = 0; i < lines.length; i++) {
      if (lines[i].includes('(`delete",') && lines[i].includes('${id}/delete`')) {
        console.log(`Found problematic line ${i + 1}: ${lines[i]}`);
        lines[i] = '  return http.request<r>("delete", `/api/v1/auth/providers/${id}/delete`);';
        console.log(`Fixed to: ${lines[i]}`);
      }
    }
    
    // <PERSON>hi lại file
    const newContent = lines.join('\n');
    fs.writeFileSync(filePath, newContent, 'utf8');
    
    console.log(`✅ Manual fix completed for: ${filePath}`);
    
    // Kiểm tra kết quả
    const verifyContent = fs.readFileSync(filePath, 'utf8');
    const verifyLines = verifyContent.split('\n');
    console.log(`Line 45 after fix: ${verifyLines[44]}`);
    
  } catch (error) {
    console.error(`❌ Error:`, error.message);
  }
}

manualFix();
